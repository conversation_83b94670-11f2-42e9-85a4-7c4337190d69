# Box Stacking Algorithm

## Interesting Papers

The papers listed below were used as inspiration for this algorithm, but there is a difference in that they assume that you have full knowledge of the boxes you need to place and the order in which you are able to do so. The algorithm this code implements only has access to a buffer of 5 boxes to place.

- [Solving Pallet Loading Problem with Real-World Constraints](https://www.qeios.com/read/M1W1U6)
- [Solving the Pallet Loading Problem](https://apps.dtic.mil/sti/tr/pdf/ADA517366.pdf)
- [A Literature Review on the Pallet Loading Problem](https://www.redalyc.org/journal/6139/613964500008/html/)
- [A Branch and Cut Algorithm for the Pallet Loading Problem](https://www.uv.es/sestio/TechRep/tr11-03.pdf)

## Information

Because this implementation of the Pallet Loading Problem has a limited set of knowledge (a buffer and not all boxes accessible), a heuristic was used based on trying to put the biggest box at the bottom. There is also a surface_match parameter which defines how much a box can overhang. This can thus be adjusted in case the objects can overhang more.

The code uses a physics simulator the drop box at a random position. Then it calculates the best drop position based on the following score:

- How close/far its away from other boxes
- The height
- The skewdness

There is also a buffer in use that's only used when the normal que doesn't provide a box.

## How the algorithm works

We simpely repeat the following algorithm:

1. Make a list of all available boxes (the first one in the que and the ones in the buffer).

2. For each of those boxes in that list, drop it at a mixture of 'smart' and random locations and calculate the score based on the methods listed above.

3. Then we compare the best location for all boxes, and select the best box to place (here we prefere to place a box directly instead of using the buffer).

4. We place the selected box and make it static for the next iterations.

## TODO

- [ ] Good way of choosing when to use buffer

## Program flow
1. Box arrives on conveyor below Helios 3D camera, which records:
   - Location
   - Rotation
   - Dimensions
2. Algorithm adds box to queue to predic stacking options.
3. Previous box is picked: new box is now pickable.
4. When a box is picked in a location, all boxes behind it advance one location.
5. If a box is in last location, but determined to be unpickable or really unfavorable, it's ejected.

## Robot flow
1. Robot receives pick and place coordinates.
2. Robot executes pick and place coordinates.

## Python Robot Communication
### Python → Robot
#### PickPlace
1. Robot: ReadyForPickPlace = True
2. Python: PickPlaceFrom = (x, y, z, roll, pitch, yaw)
3. Python: PickPlaceTo = (x, y, z, roll, pitch, yaw)
4. Python: PickPlaceStart = True
5. Robot: ConveyorLock = True
6. *Robot moves into conveyor space*
7. *Pick happens*
8. *Robot moves out of conveyor space*
9. Robot: ConveyorLock = False
10. *Place happens*
11. Robot: ReadyForPickPlace = False
12. Python: PickPlaceStart = False

### Robot → Python
#### Crash
1. Robot: Crash = True
2. Python: Crash = False
