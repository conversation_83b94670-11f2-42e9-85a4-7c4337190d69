# Conveyor Control System

This Arduino script controls a 4-part conveyor system for the Bleckmann Box Stacking project.

## System Overview

The conveyor system consists of 4 sequential parts, each with:
- A sensor at the end to detect boxes
- A motor to drive the conveyor belt
- An LED indicator to show motor status

## Hardware Requirements

### Components
- Arduino-compatible microcontroller (Arduino Uno, Nano, or similar)
- 4x Proximity sensors (digital output)
- 4x DC motors or motor drivers
- 4x LEDs for status indication
- 4x Current limiting resistors for LEDs (220Ω recommended)
- Emergency stop button (optional)

### Wiring Diagram

```
Sensors (Digital Input with Pullup):
- Part 1 Sensor → Pin 2
- Part 2 Sensor → Pin 3
- Part 3 Sensor → Pin 4
- Part 4 Sensor → Pin 5

Motors (Digital Output):
- Part 1 Motor → Pin 6
- Part 2 Motor → Pin 7
- Part 3 Motor → Pin 8
- Part 4 Motor → Pin 9

Status LEDs (Digital Output):
- Part 1 LED → Pin 10
- Part 2 LED → Pin 11
- Part 3 LED → Pin 12
- Part 4 LED → Pin 13

Optional:
- Emergency Stop → Pin A0
```

## Logic Description

### Box Transfer Rules
1. A box moves from one part to the next only if:
   - The current part has a box (sensor blocked)
   - The next part is free (sensor not blocked)
   - Both parts are running

2. Motor control logic:
   - **Part 1**: Runs when there's a box OR when Part 2 is free and ready to receive
   - **Parts 2-3**: Run when they have a box and the next part is free, OR when receiving a box from the previous part
   - **Part 4**: Runs when there's a box (to move it off the system)

### Safety Features
- Sensor debouncing to prevent false triggers
- Emergency stop functionality
- Maximum continuous run time protection
- Serial command interface for debugging

## Installation

1. Copy the files to your Arduino IDE workspace
2. Open `conveyor_control.ino` in Arduino IDE
3. Verify pin assignments in `config.h` match your hardware
4. Upload to your Arduino board

## Configuration

Edit `config.h` to customize:
- Pin assignments
- Timing parameters
- Safety settings
- Debug options

## Serial Commands

Connect to the Arduino via Serial Monitor (9600 baud) and use these commands:
- `STOP` - Emergency stop all motors
- `STATUS` - Print current system status
- `RESET` - Reset the system

## Troubleshooting

### Common Issues
1. **Motors not starting**: Check wiring and power supply
2. **False sensor triggers**: Adjust `DEBOUNCE_DELAY` in config.h
3. **Boxes getting stuck**: Verify sensor positioning and timing
4. **System not responding**: Check serial connection and baud rate

### Debug Mode
Enable `DEBUG_MODE` in config.h for detailed logging of:
- Sensor state changes
- Motor state changes
- Box transfer events

## Testing

1. **Sensor Test**: Place objects at sensor locations and verify detection
2. **Motor Test**: Manually trigger sensors and verify motor responses
3. **Transfer Test**: Place boxes and verify proper transfer between parts
4. **Emergency Stop Test**: Verify emergency stop functionality

## Maintenance

- Regularly clean sensors to prevent false readings
- Check motor connections for loose wires
- Monitor system logs for unusual behavior
- Test emergency stop functionality periodically

## Future Enhancements

Possible improvements:
- Variable motor speeds
- Box counting functionality
- Integration with higher-level control systems
- Predictive maintenance alerts
- Remote monitoring capabilities
