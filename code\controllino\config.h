/*
 * Configuration file for Conveyor Control System
 *
 * Modify these values to match your hardware setup
 */

#ifndef CONFIG_H
#define CONFIG_H

// Arduino constants and function declarations
#define HIGH 0x1
#define LOW 0x0
#define INPUT 0x0
#define OUTPUT 0x1
#define INPUT_PULLUP 0x2

// Analog pin definitions
#define A0 14
#define A1 15
#define A2 16
#define A3 17
#define A4 18
#define A5 19

// Function declarations (normally provided by Arduino.h)
extern "C"
{
  void pinMode(int pin, int mode);
  void digitalWrite(int pin, int value);
  int digitalRead(int pin);
  unsigned long millis(void);
}

// Serial class declaration
class HardwareSerial
{
public:
  void begin(long baud);
  void print(const char *str);
  void print(int value);
  void println(const char *str);
  void println(int value);
  int available();
  String readString();
};

extern HardwareSerial Serial;

// Number of conveyor parts
#define NUM_PARTS 4

// Pin assignments
#define SENSOR_PIN_1 2
#define SENSOR_PIN_2 3
#define SENSOR_PIN_3 4
#define SENSOR_PIN_4 5

#define MOTOR_PIN_1 6
#define MOTOR_PIN_2 7
#define MOTOR_PIN_3 8
#define MOTOR_PIN_4 9

#define LED_PIN_1 10
#define LED_PIN_2 11
#define LED_PIN_3 12
#define LED_PIN_4 13

// Timing configurations
#define SENSOR_READ_INTERVAL 50 // ms - How often to read sensors
#define DEBOUNCE_DELAY 100      // ms - Sensor debounce time
#define TRANSFER_DELAY 500      // ms - Minimum time for box transfer

// Motor control settings
#define MOTOR_SPEED_PWM 255 // PWM value for motor speed (0-255)
#define MOTOR_RAMP_TIME 200 // ms - Time to ramp up/down motor speed

// Safety settings
#define MAX_CONTINUOUS_RUN 30000 // ms - Maximum continuous run time
#define EMERGENCY_STOP_PIN A0    // Analog pin for emergency stop button

// Debug settings
#define DEBUG_MODE true             // Enable/disable debug output
#define STATUS_REPORT_INTERVAL 5000 // ms - How often to print status

// Sensor logic
#define SENSOR_ACTIVE_LOW true // true if sensors are active low (with pullup)

#endif // CONFIG_H
